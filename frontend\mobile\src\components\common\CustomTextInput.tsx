import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  TextInputProps,
} from 'react-native';
import { Colors } from '../../constants/Colors';

interface CustomTextInputProps extends TextInputProps {
  label: string;
  error?: string;
  showPasswordToggle?: boolean;
  onTogglePassword?: () => void;
}

export const CustomTextInput: React.FC<CustomTextInputProps> = ({
  label,
  error,
  showPasswordToggle = false,
  onTogglePassword,
  style,
  ...props
}) => {
  return (
    <View style={[styles.container, style]}>
      <Text style={styles.label}>{label}</Text>
      <View style={[styles.inputContainer, error && styles.inputError]}>
        <TextInput
          style={styles.input}
          placeholderTextColor={Colors.light.textTertiary}
          {...props}
        />
        {showPasswordToggle && (
          <TouchableOpacity
            style={[styles.passwordToggle, props.multiline && styles.passwordToggleMultiline]}
            onPress={onTogglePassword}
            activeOpacity={0.7}
          >
            <Text style={styles.passwordToggleText}>
              {props.secureTextEntry ? '👁️' : '🙈'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 8,
    marginLeft: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    paddingHorizontal: 16,
    minHeight: 50,
  },
  inputError: {
    borderColor: Colors.light.danger,
    borderWidth: 1,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    paddingVertical: 12,
  },
  passwordToggle: {
    padding: 4,
    marginLeft: 8,
    marginTop: 8,
  },
  passwordToggleMultiline: {
    alignSelf: 'flex-start',
  },
  passwordToggleText: {
    fontSize: 18,
    color: Colors.light.textTertiary,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.danger,
    marginTop: 4,
    marginLeft: 4,
  },
});
