import React, { useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TextInputProps,
} from 'react-native';
import { Colors } from '../../constants/Colors';

interface OTPInputProps {
  length: number;
  value: string[];
  onChange: (otp: string[]) => void;
  error?: boolean;
  autoFocus?: boolean;
}

export const OTPInput: React.FC<OTPInputProps> = ({
  length,
  value,
  onChange,
  error = false,
  autoFocus = true,
}) => {
  const inputRefs = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  const handleChange = (text: string, index: number) => {
    // Only allow numeric input
    if (!/^\d*$/.test(text)) return;

    const newOtp = [...value];
    newOtp[index] = text;
    onChange(newOtp);

    // Auto-focus next input
    if (text && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    // Handle backspace
    if (key === 'Backspace' && !value[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (text: string, index: number) => {
    // Handle paste operation
    const pastedText = text.replace(/\D/g, ''); // Remove non-digits
    const newOtp = [...value];
    
    for (let i = 0; i < Math.min(pastedText.length, length - index); i++) {
      newOtp[index + i] = pastedText[i];
    }
    
    onChange(newOtp);
    
    // Focus the next empty input or the last input
    const nextIndex = Math.min(index + pastedText.length, length - 1);
    inputRefs.current[nextIndex]?.focus();
  };

  return (
    <View style={styles.container}>
      {Array.from({ length }, (_, index) => (
        <TextInput
          key={index}
          ref={(ref) => (inputRefs.current[index] = ref)}
          style={[
            styles.input,
            value[index] && styles.inputFilled,
            error && styles.inputError,
          ]}
          value={value[index]}
          onChangeText={(text) => {
            if (text.length > 1) {
              // Handle paste
              handlePaste(text, index);
            } else {
              handleChange(text, index);
            }
          }}
          onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
          keyboardType="numeric"
          maxLength={1}
          selectTextOnFocus
          autoComplete="sms-otp"
          textContentType="oneTimeCode"
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  input: {
    width: 50,
    height: 60,
    borderWidth: 2,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    backgroundColor: Colors.light.card,
    textAlign: 'center',
    fontSize: 24,
    fontWeight: '600',
    color: Colors.light.text,
  },
  inputFilled: {
    borderColor: Colors.light.primary,
    backgroundColor: 'rgba(74, 111, 255, 0.05)',
  },
  inputError: {
    borderColor: Colors.light.danger,
  },
});
