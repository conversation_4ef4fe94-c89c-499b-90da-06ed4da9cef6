import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import WelcomeScreen from '../WelcomeScreen';

// Mock expo-router
const mockPush = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: jest.fn(),
    replace: jest.fn(),
  }),
}));

describe('WelcomeScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders all main content elements', () => {
      const { getByText } = render(<WelcomeScreen />);

      // App branding
      expect(getByText('HireNow')).toBeTruthy();
      expect(getByText('🤝')).toBeTruthy();

      // Main content
      expect(getByText('Find Flexible Work, Anytime')).toBeTruthy();
      expect(getByText('Connect with local opportunities that fit your schedule and skills')).toBeTruthy();

      // Action elements
      expect(getByText('Get Started')).toBeTruthy();
      expect(getByText(/Already have an account\?.*Sign In/)).toBeTruthy();
      expect(getByText('Sign In')).toBeTruthy();
    });

    it('displays the app logo and icon', () => {
      const { getByText } = render(<WelcomeScreen />);

      expect(getByText('HireNow')).toBeTruthy();
      expect(getByText('🤝')).toBeTruthy();
    });

    it('displays the correct subtitle text', () => {
      const { getByText } = render(<WelcomeScreen />);

      expect(getByText('Connect with local opportunities that fit your schedule and skills')).toBeTruthy();
    });
  });

  describe('User Interactions', () => {
    it('handles get started button press', () => {
      const { getByText } = render(<WelcomeScreen />);

      const getStartedButton = getByText('Get Started');
      fireEvent.press(getStartedButton);

      expect(mockPush).toHaveBeenCalledWith('/auth/register');
    });

    it('displays sign in text but does not handle press (commented out)', () => {
      const { getByText } = render(<WelcomeScreen />);

      const signInText = getByText(/Already have an account\?.*Sign In/);
      expect(signInText).toBeTruthy();

      // The sign in functionality is commented out in the component
      // so we just verify the text is displayed
      expect(getByText('Sign In')).toBeTruthy();
    });

    it('has accessible button elements', () => {
      const { getByText } = render(<WelcomeScreen />);

      const getStartedButton = getByText('Get Started');
      expect(getStartedButton).toBeTruthy();

      // Verify button is pressable
      fireEvent.press(getStartedButton);
      expect(mockPush).toHaveBeenCalled();
    });
  });
});
