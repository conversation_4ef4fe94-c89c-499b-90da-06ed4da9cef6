import React from 'react';
import { View } from 'react-native';

// Mock LinearGradient component that properly handles children and props
export const LinearGradient = React.forwardRef(({ children, style, colors, start, end, ...props }, ref) => {
  return (
    <View 
      ref={ref} 
      style={[
        style,
        {
          // Add a simple background color to simulate gradient
          backgroundColor: colors && colors.length > 0 ? colors[0] : 'transparent'
        }
      ]} 
      {...props}
    >
      {children}
    </View>
  );
});

LinearGradient.displayName = 'LinearGradient';

export default { LinearGradient };
