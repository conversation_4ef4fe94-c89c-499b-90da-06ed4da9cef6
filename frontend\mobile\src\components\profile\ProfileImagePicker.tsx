import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Image,
} from 'react-native';
import { Colors } from '../../constants/Colors';

interface ProfileImagePickerProps {
  imageUri?: string;
  onImageSelect: (imageUri: string) => void;
  label: string;
}

export const ProfileImagePicker: React.FC<ProfileImagePickerProps> = ({
  imageUri,
  onImageSelect,
  label,
}) => {
  const handleImagePicker = () => {
    Alert.alert(
      'Select Image',
      'Choose how you want to select your image',
      [
        {
          text: 'Camera',
          onPress: () => {
            // Simulate camera selection
            const mockImageUri = 'https://via.placeholder.com/150/4A6FFF/FFFFFF?text=Photo';
            onImageSelect(mockImageUri);
          },
        },
        {
          text: 'Gallery',
          onPress: () => {
            // Simulate gallery selection
            const mockImageUri = 'https://via.placeholder.com/150/63E2FF/FFFFFF?text=Gallery';
            onImageSelect(mockImageUri);
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      
      <TouchableOpacity
        style={styles.imageContainer}
        onPress={handleImagePicker}
        activeOpacity={0.8}
      >
        {imageUri ? (
          <Image source={{ uri: imageUri }} style={styles.image} />
        ) : (
          <View style={styles.placeholder}>
            <Text style={styles.placeholderIcon}>📷</Text>
            <Text style={styles.placeholderText}>Tap to add photo</Text>
          </View>
        )}
        
        {/* Edit overlay */}
        <View style={styles.editOverlay}>
          <Text style={styles.editIcon}>✏️</Text>
        </View>
      </TouchableOpacity>
      
      <Text style={styles.helpText}>
        {label === 'Profile Photo' 
          ? 'Add a professional photo to help employers recognize you'
          : 'Upload your company logo to build brand recognition'
        }
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginBottom: 30,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 15,
  },
  imageContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    overflow: 'hidden',
    position: 'relative',
    marginBottom: 10,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  placeholder: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(74, 111, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(74, 111, 255, 0.3)',
    borderStyle: 'dashed',
  },
  placeholderIcon: {
    fontSize: 32,
    marginBottom: 5,
  },
  placeholderText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  editOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.light.card,
  },
  editIcon: {
    fontSize: 14,
  },
  helpText: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    textAlign: 'center',
    lineHeight: 16,
    paddingHorizontal: 20,
  },
});
