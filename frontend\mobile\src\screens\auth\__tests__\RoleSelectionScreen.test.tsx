import React from 'react';
import { ActivityIndicator } from 'react-native';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import RoleSelectionScreen from '../RoleSelectionScreen';

// Mock expo-router for this specific test
const mockPush = jest.fn();
const mockBack = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
    replace: jest.fn(),
  }),
}));

describe('RoleSelectionScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock call counts
    mockPush.mockClear();
    mockBack.mockClear();
  });

  const renderRoleSelectionScreen = () => {
    return render(<RoleSelectionScreen key={Math.random()} />);
  };

  it('renders correctly', () => {
    const { getByText } = render(<RoleSelectionScreen />);
    
    expect(getByText('Choose Your Role')).toBeTruthy();
    expect(getByText('Select how you\'d like to use HireNow to get started')).toBeTruthy();
    expect(getByText('Job Seeker')).toBeTruthy();
    expect(getByText('Employer')).toBeTruthy();
    expect(getByText('Continue')).toBeTruthy();
  });

  it('displays role descriptions and features', () => {
    const { getByText } = render(<RoleSelectionScreen />);
    
    // Job Seeker features
    expect(getByText('Find flexible work opportunities that match your skills and schedule')).toBeTruthy();
    expect(getByText('Browse available jobs')).toBeTruthy();
    expect(getByText('Apply with one tap')).toBeTruthy();
    expect(getByText('Flexible scheduling')).toBeTruthy();
    
    // Employer features
    expect(getByText('Post jobs and find qualified workers for your business needs')).toBeTruthy();
    expect(getByText('Post job opportunities')).toBeTruthy();
    expect(getByText('Review applications')).toBeTruthy();
    expect(getByText('Manage your workforce')).toBeTruthy();
  });

  it('allows role selection', async () => {
    const { getByTestId, getByText } = renderRoleSelectionScreen();

    const jobSeekerCard = getByTestId('role-card-job_seeker');
    const continueButton = getByText('Continue');

    // Initially continue button should be disabled (test by trying to press it)
    fireEvent.press(continueButton);
    expect(mockPush).not.toHaveBeenCalled();

    // Select Job Seeker role
    fireEvent.press(jobSeekerCard);

    // Now the continue button should work
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledTimes(1);
      expect(mockPush).toHaveBeenCalledWith('/auth/profile-setup?role=job_seeker');
    }, { timeout: 3000 }); // Wait up to 3 seconds for the async operation
  });

  it('switches between role selections', async () => {
    const { getByTestId, getByText } = renderRoleSelectionScreen();

    const jobSeekerCard = getByTestId('role-card-job_seeker');
    const employerCard = getByTestId('role-card-employer');

    // Select Job Seeker first
    fireEvent.press(jobSeekerCard);

    // Then select Employer
    fireEvent.press(employerCard);

    // Both actions should work without errors
    await waitFor(() => {
      expect(getByText('Continue')).toBeTruthy();
    });
  });

  it('handles continue button press', async () => {
    const { getByTestId, getByText } = renderRoleSelectionScreen();

    const jobSeekerCard = getByTestId('role-card-job_seeker');
    const continueButton = getByText('Continue');

    // Select a role first
    fireEvent.press(jobSeekerCard);

    // Press continue
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledTimes(1);
      expect(mockPush).toHaveBeenCalledWith('/auth/profile-setup?role=job_seeker');
    }, { timeout: 3000 });
  });

  it('handles back button press', () => {
    const { getByText } = render(<RoleSelectionScreen />);
    const backButton = getByText('←');

    fireEvent.press(backButton);

    expect(mockBack).toHaveBeenCalled();
  });

  it('shows help text', () => {
    const { getByText } = render(<RoleSelectionScreen />);
    
    expect(getByText('You can change your role later in settings')).toBeTruthy();
  });

  it('displays role icons', () => {
    const { getByText } = render(<RoleSelectionScreen />);

    expect(getByText('👤')).toBeTruthy(); // Job Seeker icon
    expect(getByText('🏢')).toBeTruthy(); // Employer icon
  });

  it('handles employer role selection and navigation', async () => {
    const { getByTestId, getByText } = renderRoleSelectionScreen();

    const employerCard = getByTestId('role-card-employer');
    const continueButton = getByText('Continue');

    // Select employer role
    fireEvent.press(employerCard);

    // Press continue
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledTimes(1);
      expect(mockPush).toHaveBeenCalledWith('/auth/profile-setup?role=employer');
    }, { timeout: 3000 });
  });

  it('disables continue button when no role is selected', () => {
    const { getByText } = renderRoleSelectionScreen();

    const continueButton = getByText('Continue');

    // Button should be disabled initially (test by trying to press it)
    fireEvent.press(continueButton);
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('enables continue button when a role is selected', async () => {
    const { getByTestId, getByText } = renderRoleSelectionScreen();

    const jobSeekerCard = getByTestId('role-card-job_seeker');
    const continueButton = getByText('Continue');

    // Select a role
    fireEvent.press(jobSeekerCard);

    // Button should now work
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledTimes(1);
      expect(mockPush).toHaveBeenCalledWith('/auth/profile-setup?role=job_seeker');
    }, { timeout: 3000 });
  });

  it('shows loading state during role selection submission', async () => {
    const { getByTestId, getByText, UNSAFE_getByType } = renderRoleSelectionScreen();

    const jobSeekerCard = getByTestId('role-card-job_seeker');
    const continueButton = getByText('Continue');

    // Select a role
    fireEvent.press(jobSeekerCard);

    // Press continue
    fireEvent.press(continueButton);

    // Should show loading state (ActivityIndicator)
    expect(UNSAFE_getByType(ActivityIndicator)).toBeTruthy();
  });

  it('displays proper role selection visual feedback', async () => {
    const { getByTestId, getByText } = renderRoleSelectionScreen();

    const jobSeekerCard = getByTestId('role-card-job_seeker');
    const employerCard = getByTestId('role-card-employer');

    // Select job seeker
    fireEvent.press(jobSeekerCard);

    // Then select employer (should deselect job seeker)
    fireEvent.press(employerCard);

    // Both actions should work without errors
    await waitFor(() => {
      expect(getByText('Continue')).toBeTruthy();
    });
  });
});
