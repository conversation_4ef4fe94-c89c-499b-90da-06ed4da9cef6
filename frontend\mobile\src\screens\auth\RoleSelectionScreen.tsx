import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Colors } from '../../constants/Colors';
import { CustomButton } from '../../components/common/CustomButton';
import { RoleCard } from '../../components/auth/RoleCard';

interface Role {
  id: 'job_seeker' | 'employer';
  title: string;
  description: string;
  icon: string;
  features: string[];
}

const roles: Role[] = [
  {
    id: 'job_seeker',
    title: 'Job Seeker',
    description: 'Find flexible work opportunities that match your skills and schedule',
    icon: '👤',
    features: [
      'Browse available jobs',
      'Apply with one tap',
      'Track application status',
      'Flexible scheduling',
      'Skill-based matching',
    ],
  },
  {
    id: 'employer',
    title: 'Employer',
    description: 'Post jobs and find qualified workers for your business needs',
    icon: '🏢',
    features: [
      'Post job opportunities',
      'Review applications',
      'Manage your workforce',
      'Real-time notifications',
      'Performance tracking',
    ],
  },
];

export default function RoleSelectionScreen() {
  const router = useRouter();
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleRoleSelect = (roleId: string) => {
    setSelectedRole(roleId);
  };

  const handleContinue = async () => {
    if (!selectedRole) return;

    try {
      setIsLoading(true);

      // Simulate API call to save role selection
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Navigate to profile setup with role context
      router.push(`/auth/profile-setup?role=${selectedRole}`);
    } catch (error) {
      console.error('Error saving role selection:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };



  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.background} />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
            activeOpacity={0.7}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          
          <View style={styles.headerContent}>
            <Text style={styles.title}>Choose Your Role</Text>
            <Text style={styles.subtitle}>
              Select how you'd like to use HireNow to get started
            </Text>
          </View>
        </View>

        {/* Role Cards */}
        <View style={styles.rolesContainer}>
          {roles.map((role) => (
            <RoleCard
              key={role.id}
              role={role}
              isSelected={selectedRole === role.id}
              onSelect={handleRoleSelect}
            />
          ))}
        </View>

        {/* Continue Button */}
        <View style={styles.actionContainer}>
          <CustomButton
            title="Continue"
            onPress={handleContinue}
            loading={isLoading}
            disabled={!selectedRole || isLoading}
            style={styles.continueButton}
          />
          
          <Text style={styles.helpText}>
            You can change your role later in settings
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.card,
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backButtonText: {
    fontSize: 20,
    color: Colors.light.text,
    fontWeight: '600',
  },
  headerContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 10,
  },
  rolesContainer: {
    paddingHorizontal: 20,
    gap: 20,
  },
  actionContainer: {
    paddingHorizontal: 20,
    paddingTop: 30,
    alignItems: 'center',
  },
  continueButton: {
    marginBottom: 15,
  },
  helpText: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    textAlign: 'center',
  },
});
