import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  TextInput,
} from 'react-native';
import { Colors } from '../../constants/Colors';

interface LocationSelectorProps {
  label: string;
  selectedLocation: string;
  onLocationChange: (location: string) => void;
  error?: string;
}

interface Location {
  id: string;
  name: string;
  state: string;
  country: string;
}

const malaysianLocations: Location[] = [
  // Kuala Lumpur
  { id: '1', name: 'Kuala Lumpur', state: 'Federal Territory', country: 'Malaysia' },
  { id: '2', name: 'Petaling Jaya', state: 'Selangor', country: 'Malaysia' },
  { id: '3', name: 'Subang Jaya', state: 'Selangor', country: 'Malaysia' },
  { id: '4', name: 'Shah Alam', state: 'Selangor', country: 'Malaysia' },
  { id: '5', name: 'Klang', state: 'Selangor', country: 'Malaysia' },
  
  // Penang
  { id: '6', name: 'George Town', state: 'Penang', country: 'Malaysia' },
  { id: '7', name: 'Butter<PERSON>', state: 'Penang', country: 'Malaysia' },
  
  // Johor
  { id: '8', name: 'Johor Bahru', state: 'Johor', country: 'Malaysia' },
  { id: '9', name: 'Skudai', state: 'Johor', country: 'Malaysia' },
  
  // Other major cities
  { id: '10', name: 'Ipoh', state: 'Perak', country: 'Malaysia' },
  { id: '11', name: 'Malacca City', state: 'Malacca', country: 'Malaysia' },
  { id: '12', name: 'Kota Kinabalu', state: 'Sabah', country: 'Malaysia' },
  { id: '13', name: 'Kuching', state: 'Sarawak', country: 'Malaysia' },
  { id: '14', name: 'Kota Bharu', state: 'Kelantan', country: 'Malaysia' },
  { id: '15', name: 'Kuantan', state: 'Pahang', country: 'Malaysia' },
  
  // Remote work option
  { id: '16', name: 'Remote Work', state: 'Anywhere', country: 'Malaysia' },
];

export const LocationSelector: React.FC<LocationSelectorProps> = ({
  label,
  selectedLocation,
  onLocationChange,
  error,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredLocations = malaysianLocations.filter(location =>
    location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    location.state.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleLocationSelect = (location: Location) => {
    const locationString = location.name === 'Remote Work' 
      ? 'Remote Work'
      : `${location.name}, ${location.state}`;
    onLocationChange(locationString);
    setShowModal(false);
    setSearchQuery('');
  };

  const renderLocationItem = ({ item }: { item: Location }) => (
    <TouchableOpacity
      style={styles.locationItem}
      onPress={() => handleLocationSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.locationInfo}>
        <Text style={styles.locationName}>{item.name}</Text>
        <Text style={styles.locationState}>
          {item.name === 'Remote Work' ? item.state : `${item.state}, ${item.country}`}
        </Text>
      </View>
      {item.name === 'Remote Work' && (
        <Text style={styles.remoteIcon}>🌐</Text>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      
      <TouchableOpacity
        style={[
          styles.selector,
          error && styles.selectorError,
        ]}
        onPress={() => setShowModal(true)}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.selectorText,
          !selectedLocation && styles.placeholderText,
        ]}>
          {selectedLocation || 'Select your location'}
        </Text>
        <Text style={styles.dropdownIcon}>▼</Text>
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      {/* Location Selection Modal */}
      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Location</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowModal(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            {/* Search Input */}
            <View style={styles.searchContainer}>
              <TextInput
                style={styles.searchInput}
                placeholder="Search locations..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoFocus
              />
            </View>

            {/* Locations List */}
            <FlatList
              data={filteredLocations}
              renderItem={renderLocationItem}
              keyExtractor={(item) => item.id}
              style={styles.locationsList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 25,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.light.card,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 50,
  },
  selectorError: {
    borderColor: Colors.light.danger,
  },
  selectorText: {
    fontSize: 16,
    color: Colors.light.text,
    flex: 1,
  },
  placeholderText: {
    color: Colors.light.textTertiary,
  },
  dropdownIcon: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    marginLeft: 10,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.danger,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.light.card,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  closeButton: {
    padding: 4,
  },
  closeButtonText: {
    fontSize: 18,
    color: Colors.light.textSecondary,
  },
  searchContainer: {
    padding: 20,
    paddingBottom: 10,
  },
  searchInput: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  locationsList: {
    flex: 1,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 2,
  },
  locationState: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  remoteIcon: {
    fontSize: 20,
    marginLeft: 10,
  },
});
