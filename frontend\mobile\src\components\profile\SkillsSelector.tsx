import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
} from 'react-native';
import { Colors } from '../../constants/Colors';

interface SkillsSelectorProps {
  label: string;
  selectedSkills: string[];
  onSkillsChange: (skills: string[]) => void;
  error?: string;
}

const popularSkills = [
  'JavaScript',
  'React Native',
  'Node.js',
  'Python',
  'Java',
  'Customer Service',
  'Sales',
  'Marketing',
  'Data Analysis',
  'Project Management',
  'Graphic Design',
  'Content Writing',
  'Social Media',
  'Photography',
  'Video Editing',
  'Accounting',
  'HR Management',
  'Teaching',
  'Nursing',
  'Cooking',
  'Cleaning',
  'Driving',
  'Delivery',
  'Warehouse',
  'Construction',
];

export const SkillsSelector: React.FC<SkillsSelectorProps> = ({
  label,
  selectedSkills,
  onSkillsChange,
  error,
}) => {
  const [customSkill, setCustomSkill] = useState('');
  const [showAllSkills, setShowAllSkills] = useState(false);

  const displayedSkills = showAllSkills ? popularSkills : popularSkills.slice(0, 12);

  const handleSkillToggle = (skill: string) => {
    if (selectedSkills.includes(skill)) {
      onSkillsChange(selectedSkills.filter(s => s !== skill));
    } else {
      onSkillsChange([...selectedSkills, skill]);
    }
  };

  const handleAddCustomSkill = () => {
    if (customSkill.trim() && !selectedSkills.includes(customSkill.trim())) {
      onSkillsChange([...selectedSkills, customSkill.trim()]);
      setCustomSkill('');
    }
  };

  const handleRemoveSkill = (skill: string) => {
    onSkillsChange(selectedSkills.filter(s => s !== skill));
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      
      {/* Selected Skills */}
      {selectedSkills.length > 0 && (
        <View style={styles.selectedContainer}>
          <Text style={styles.selectedLabel}>Selected Skills:</Text>
          <View style={styles.selectedSkills}>
            {selectedSkills.map((skill, index) => (
              <TouchableOpacity
                key={index}
                style={styles.selectedSkill}
                onPress={() => handleRemoveSkill(skill)}
                activeOpacity={0.7}
              >
                <Text style={styles.selectedSkillText}>{skill}</Text>
                <Text style={styles.removeIcon}>×</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Popular Skills */}
      <Text style={styles.sectionLabel}>Popular Skills:</Text>
      <View style={styles.skillsGrid}>
        {displayedSkills.map((skill, index) => {
          const isSelected = selectedSkills.includes(skill);
          return (
            <TouchableOpacity
              key={index}
              style={[
                styles.skillChip,
                isSelected && styles.skillChipSelected,
              ]}
              onPress={() => handleSkillToggle(skill)}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.skillChipText,
                isSelected && styles.skillChipTextSelected,
              ]}>
                {skill}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Show More/Less Button */}
      <TouchableOpacity
        style={styles.showMoreButton}
        onPress={() => setShowAllSkills(!showAllSkills)}
        activeOpacity={0.7}
      >
        <Text style={styles.showMoreText}>
          {showAllSkills ? 'Show Less' : 'Show More Skills'}
        </Text>
      </TouchableOpacity>

      {/* Custom Skill Input */}
      <View style={styles.customSkillContainer}>
        <Text style={styles.sectionLabel}>Add Custom Skill:</Text>
        <View style={styles.customSkillInput}>
          <TextInput
            style={styles.textInput}
            placeholder="Type a skill..."
            value={customSkill}
            onChangeText={setCustomSkill}
            onSubmitEditing={handleAddCustomSkill}
            returnKeyType="done"
          />
          <TouchableOpacity
            style={[
              styles.addButton,
              !customSkill.trim() && styles.addButtonDisabled,
            ]}
            onPress={handleAddCustomSkill}
            disabled={!customSkill.trim()}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.addButtonText,
              !customSkill.trim() && styles.addButtonTextDisabled,
            ]}>
              Add
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 25,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 15,
  },
  selectedContainer: {
    marginBottom: 20,
  },
  selectedLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 8,
  },
  selectedSkills: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedSkill: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  selectedSkillText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  removeIcon: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 10,
  },
  skillsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 15,
  },
  skillChip: {
    backgroundColor: Colors.light.card,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  skillChipSelected: {
    backgroundColor: 'rgba(74, 111, 255, 0.1)',
    borderColor: Colors.light.primary,
  },
  skillChipText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  skillChipTextSelected: {
    color: Colors.light.primary,
    fontWeight: '500',
  },
  showMoreButton: {
    alignSelf: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  showMoreText: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '500',
  },
  customSkillContainer: {
    marginBottom: 10,
  },
  customSkillInput: {
    flexDirection: 'row',
    gap: 10,
  },
  textInput: {
    flex: 1,
    backgroundColor: Colors.light.card,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  addButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    justifyContent: 'center',
  },
  addButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  addButtonTextDisabled: {
    color: '#999999',
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.danger,
    marginTop: 4,
  },
});
