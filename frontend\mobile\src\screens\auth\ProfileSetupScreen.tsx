import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
} from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Colors } from '../../constants/Colors';
import { CustomTextInput } from '../../components/common/CustomTextInput';
import { CustomButton } from '../../components/common/CustomButton';
import { ProfileImagePicker } from '../../components/profile/ProfileImagePicker';
import { SkillsSelector } from '../../components/profile/SkillsSelector';
import { LocationSelector } from '../../components/profile/LocationSelector';

interface JobSeekerProfile {
  profileImage?: string;
  bio: string;
  skills: string[];
  location: string;
  availability: 'full-time' | 'part-time' | 'flexible';
  experience: string;
  hourlyRate: string;
}

interface EmployerProfile {
  profileImage?: string;
  companyName: string;
  companyDescription: string;
  industry: string;
  location: string;
  companySize: string;
  website: string;
}

type ProfileFormData = JobSeekerProfile | EmployerProfile;

export default function ProfileSetupScreen() {
  const router = useRouter();
  const { role } = useLocalSearchParams<{ role: 'job_seeker' | 'employer' }>();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [selectedLocation, setSelectedLocation] = useState('');
  const [profileImage, setProfileImage] = useState<string | undefined>();

  const isJobSeeker = role === 'job_seeker';

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm<ProfileFormData>({
    mode: 'onChange',
    defaultValues: isJobSeeker
      ? {
          bio: '',
          skills: [],
          location: '',
          availability: 'flexible',
          experience: '',
          hourlyRate: '',
        }
      : {
          companyName: '',
          companyDescription: '',
          industry: '',
          location: '',
          companySize: '',
          website: '',
        },
  });

  useEffect(() => {
    if (selectedSkills.length > 0 && isJobSeeker) {
      setValue('skills' as keyof ProfileFormData, selectedSkills as any);
    }
  }, [selectedSkills, setValue, isJobSeeker]);

  useEffect(() => {
    if (selectedLocation) {
      setValue('location' as keyof ProfileFormData, selectedLocation as any);
    }
  }, [selectedLocation, setValue]);

  const handleImageSelect = (imageUri: string) => {
    setProfileImage(imageUri);
    setValue('profileImage' as keyof ProfileFormData, imageUri as any);
  };

  const onSubmit = async (data: ProfileFormData) => {
    try {
      setIsLoading(true);

      // Simulate API call to save profile
      await new Promise(resolve => setTimeout(resolve, 2000));

      Alert.alert(
        'Success!',
        'Your profile has been created successfully. Welcome to HireNow!',
        [
          {
            text: 'Continue',
            onPress: () => {
              // Navigate to main app (tabs)
              router.replace('/(tabs)');
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const renderJobSeekerForm = () => (
    <>
      {/* Bio */}
      <Controller
        control={control}
        name="bio"
        rules={{
          required: 'Bio is required',
          minLength: {
            value: 20,
            message: 'Bio must be at least 20 characters',
          },
          maxLength: {
            value: 500,
            message: 'Bio must be less than 500 characters',
          },
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <CustomTextInput
            label="Bio"
            placeholder="Tell employers about yourself, your experience, and what you're looking for..."
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.bio?.message}
            multiline
            numberOfLines={4}
            style={styles.textArea}
          />
        )}
      />

      {/* Skills */}
      <SkillsSelector
        label="Skills"
        selectedSkills={selectedSkills}
        onSkillsChange={setSelectedSkills}
        error={selectedSkills.length === 0 ? 'Please select at least one skill' : undefined}
      />

      {/* Experience Level */}
      <Controller
        control={control}
        name="experience"
        rules={{ required: 'Experience level is required' }}
        render={({ field: { onChange, onBlur, value } }) => (
          <CustomTextInput
            label="Experience Level"
            placeholder="e.g., Entry Level, 2-5 years, Senior"
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.experience?.message}
          />
        )}
      />

      {/* Hourly Rate */}
      <Controller
        control={control}
        name="hourlyRate"
        rules={{
          required: 'Hourly rate is required',
          pattern: {
            value: /^\d+(\.\d{1,2})?$/,
            message: 'Please enter a valid hourly rate',
          },
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <CustomTextInput
            label="Expected Hourly Rate (RM)"
            placeholder="25.00"
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.hourlyRate?.message}
            keyboardType="decimal-pad"
          />
        )}
      />
    </>
  );

  const renderEmployerForm = () => (
    <>
      {/* Company Name */}
      <Controller
        control={control}
        name="companyName"
        rules={{
          required: 'Company name is required',
          minLength: {
            value: 2,
            message: 'Company name must be at least 2 characters',
          },
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <CustomTextInput
            label="Company Name"
            placeholder="Your Company Ltd."
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.companyName?.message}
          />
        )}
      />

      {/* Company Description */}
      <Controller
        control={control}
        name="companyDescription"
        rules={{
          required: 'Company description is required',
          minLength: {
            value: 50,
            message: 'Description must be at least 50 characters',
          },
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <CustomTextInput
            label="Company Description"
            placeholder="Describe your company, what you do, and your mission..."
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.companyDescription?.message}
            multiline
            numberOfLines={4}
            style={styles.textArea}
          />
        )}
      />

      {/* Industry */}
      <Controller
        control={control}
        name="industry"
        rules={{ required: 'Industry is required' }}
        render={({ field: { onChange, onBlur, value } }) => (
          <CustomTextInput
            label="Industry"
            placeholder="e.g., Technology, Healthcare, Retail"
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.industry?.message}
          />
        )}
      />

      {/* Company Size */}
      <Controller
        control={control}
        name="companySize"
        rules={{ required: 'Company size is required' }}
        render={({ field: { onChange, onBlur, value } }) => (
          <CustomTextInput
            label="Company Size"
            placeholder="e.g., 1-10, 11-50, 51-200, 200+"
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.companySize?.message}
          />
        )}
      />

      {/* Website */}
      <Controller
        control={control}
        name="website"
        rules={{
          pattern: {
            value: /^https?:\/\/.+\..+/,
            message: 'Please enter a valid website URL',
          },
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <CustomTextInput
            label="Website (Optional)"
            placeholder="https://yourcompany.com"
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.website?.message}
            keyboardType="url"
            autoCapitalize="none"
          />
        )}
      />
    </>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.background} />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
            activeOpacity={0.7}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          
          <View style={styles.headerContent}>
            <Text style={styles.title}>Complete Your Profile</Text>
            <Text style={styles.subtitle}>
              {isJobSeeker 
                ? 'Help employers find you by completing your profile'
                : 'Set up your company profile to attract the best talent'
              }
            </Text>
          </View>
        </View>

        {/* Form */}
        <View style={styles.form}>
          {/* Profile Image */}
          <ProfileImagePicker
            imageUri={profileImage}
            onImageSelect={handleImageSelect}
            label={isJobSeeker ? 'Profile Photo' : 'Company Logo'}
          />

          {/* Dynamic Form Fields */}
          {isJobSeeker ? renderJobSeekerForm() : renderEmployerForm()}

          {/* Location */}
          <LocationSelector
            label="Location"
            selectedLocation={selectedLocation}
            onLocationChange={setSelectedLocation}
            error={!selectedLocation ? 'Location is required' : undefined}
          />

          {/* Submit Button */}
          <CustomButton
            title="Complete Setup"
            onPress={handleSubmit(onSubmit)}
            loading={isLoading}
            disabled={!isValid || isLoading || !selectedLocation || (isJobSeeker && selectedSkills.length === 0)}
            style={styles.submitButton}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.card,
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backButtonText: {
    fontSize: 20,
    color: Colors.light.text,
    fontWeight: '600',
  },
  headerContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 10,
  },
  form: {
    paddingHorizontal: 20,
  },
  textArea: {
    minHeight: 100,
  },
  submitButton: {
    marginTop: 30,
    marginBottom: 20,
  },
});
